/**
 * MCP tool for intelligently adding translations with conflict detection
 */

import { z } from 'zod';
import { TranslationExtractor, ExtractionUtils } from '../core/translation-extractor.js';
import { TranslationIndex } from '../core/translation-index.js';

/**
 * Setup the smart add translation tool
 */
export function setupAddTranslationSmartTool(server: any, index: TranslationIndex, config: any) {
  server.tool(
    'add_translation_smart',
    'Intelligently add translation with conflict detection and key generation',
    {
      text: z.string().describe('Text to add as translation (base language)'),
      languages: z.record(z.string(), z.string()).describe('Translations by language code'),
      suggestedKey: z.string().optional().describe('Suggested key path'),
      context: z.string().optional().describe('Context/component where it will be used'),
      autoGenerateKey: z.boolean().default(true).describe('Auto-generate key if not provided'),
      keyStyle: z.enum(['nested', 'flat', 'camelCase', 'kebab-case']).optional().describe('Key naming style'),
      checkSimilar: z.boolean().default(true).describe('Check for similar existing translations'),
      overwrite: z.boolean().default(false).describe('Overwrite existing translation if key exists'),
      validateStructure: z.boolean().default(true).describe('Validate structure consistency after adding')
    },
    async ({ 
      text, 
      languages, 
      suggestedKey, 
      context, 
      autoGenerateKey,
      keyStyle,
      checkSimilar,
      overwrite,
      validateStructure
    }: {
      text: string;
      languages: Record<string, string>;
      suggestedKey?: string;
      context?: string;
      autoGenerateKey: boolean;
      keyStyle?: 'nested' | 'flat' | 'camelCase' | 'kebab-case';
      checkSimilar: boolean;
      overwrite: boolean;
      validateStructure: boolean;
    }) => {
      try {
        const extractor = new TranslationExtractor();
        const style = keyStyle || config.keyStyle || 'nested';
        const baseLanguage = config.baseLanguage || 'en';

        // Generate key if not provided
        let keyPath = suggestedKey;
        if (!keyPath && autoGenerateKey) {
          keyPath = extractor.generateSmartKey(text, context, style);
        }

        if (!keyPath) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'No key path provided and auto-generation failed',
                suggestion: 'Provide a suggestedKey or enable autoGenerateKey'
              }, null, 2)
            }]
          };
        }

        // Validate key format
        if (!ExtractionUtils.validateKeyFormat(keyPath, style)) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Invalid key format',
                key: keyPath,
                expectedFormat: style,
                suggestion: extractor.generateSmartKey(text, context, style)
              }, null, 2)
            }]
          };
        }

        // Check if key already exists
        if (index.has(keyPath) && !overwrite) {
          const existing = index.get(keyPath, baseLanguage);
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Translation key already exists',
                key: keyPath,
                existingValue: existing && typeof existing === 'object' && 'value' in existing ? existing.value : existing,
                suggestion: 'Use overwrite=true to replace existing translation',
                alternativeKey: `${keyPath}_alt`
              }, null, 2)
            }]
          };
        }

        // Check for similar existing translations
        let similarTranslations: any[] = [];
        if (checkSimilar) {
          similarTranslations = await ExtractionUtils.findSimilarTranslations(
            text,
            index,
            0.7
          );

          if (similarTranslations.length > 0 && !overwrite) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  warning: 'Similar translations found',
                  suggestedKey: keyPath,
                  baseText: text,
                  similarTranslations,
                  proceed: false,
                  recommendation: 'Review similar translations or use overwrite=true to proceed anyway'
                }, null, 2)
              }]
            };
          }
        }

        // Prepare all languages (include base language)
        const allLanguages = { ...languages };
        if (!allLanguages[baseLanguage]) {
          allLanguages[baseLanguage] = text;
        }

        // Validate all translations are provided
        const availableLanguages = index.getLanguages();
        const missingLanguages = availableLanguages.filter(lang => !allLanguages[lang]);
        
        if (missingLanguages.length > 0 && validateStructure) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                warning: 'Missing translations for some languages',
                key: keyPath,
                missingLanguages,
                providedLanguages: Object.keys(allLanguages),
                recommendation: 'Provide translations for all languages or disable validateStructure'
              }, null, 2)
            }]
          };
        }

        // Add the translations
        const operations = Object.entries(allLanguages).map(([lang, value]) => ({
          type: 'set' as const,
          keyPath,
          language: lang,
          value
        }));

        const result = await index.batchUpdate(operations);

        if (!result.success) {
          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                error: 'Failed to add translations',
                key: keyPath,
                errors: result.errors
              }, null, 2)
            }]
          };
        }

        // Validate structure if requested
        let structureValidation = null;
        if (validateStructure) {
          try {
            structureValidation = await index.validateStructure({
              baseLanguage,
              autoFix: false
            });
          } catch (error) {
            // Structure validation failed, but translation was added
            structureValidation = {
              error: error instanceof Error ? error.message : 'Unknown validation error'
            };
          }
        }

        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: true,
              key: keyPath,
              baseText: text,
              addedLanguages: Object.keys(allLanguages),
              keyStyle: style,
              overwritten: overwrite && index.has(keyPath),
              similarTranslationsFound: similarTranslations.length,
              structureValidation: structureValidation ? {
                valid: structureValidation.valid,
                issues: structureValidation.valid ? null : {
                  missingKeys: Object.keys(structureValidation.missingKeys || {}).length,
                  extraKeys: Object.keys(structureValidation.extraKeys || {}).length,
                  typeMismatches: (structureValidation.typeMismatches || []).length
                }
              } : null
            }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: `Error adding translation: ${error instanceof Error ? error.message : 'Unknown error'}`
          }]
        };
      }
    }
  );
}
