/**
 * MCP tool for bulk adding translations with smart conflict detection
 */

import { z } from 'zod';
import { TranslationExtractor, ExtractionUtils } from '../core/translation-extractor.js';
import { TranslationIndex } from '../core/translation-index.js';
import { promises as fs } from 'fs';
import { join } from 'path';

interface BulkTranslationEntry {
  text: string;
  languages: Record<string, string>;
  suggestedKey?: string;
  context?: string;
}

interface BulkTranslationResult {
  success: boolean;
  key: string;
  originalText: string;
  addedLanguages: string[];
  fileWriteResults: Record<string, { success: boolean; error?: string }>;
  skipped?: boolean;
  skipReason?: string;
  generatedKey?: boolean;
}

/**
 * Setup the bulk add translations smart tool
 */
export function setupBulkAddTranslationsSmartTool(server: any, index: TranslationIndex, config: any) {
  server.tool(
    'bulk_add_translations_smart',
    'Add multiple translations in a single operation with smart conflict detection',
    {
      translations: z.array(z.object({
        text: z.string().describe('Text to add as translation (base language)'),
        languages: z.record(z.string(), z.string()).describe('Translations by language code'),
        suggestedKey: z.string().optional().describe('Suggested key path'),
        context: z.string().optional().describe('Context/component where it will be used')
      })).describe('Array of translations to add'),
      options: z.object({
        autoGenerateKeys: z.boolean().default(true).describe('Auto-generate keys if not provided'),
        keyStyle: z.enum(['nested', 'flat', 'camelCase', 'kebab-case']).optional().describe('Key naming style'),
        checkSimilar: z.boolean().default(false).describe('Check for similar existing translations'),
        overwrite: z.boolean().default(false).describe('Overwrite existing translations'),
        validateStructure: z.boolean().default(false).describe('Validate translation structure after adding'),
        skipOnError: z.boolean().default(true).describe('Skip individual entries on error instead of failing entire batch'),
        batchSize: z.number().min(1).max(100).default(50).describe('Process translations in batches of this size')
      }).default({}).describe('Bulk operation options')
    },
    async ({ translations, options }: {
      translations: BulkTranslationEntry[];
      options: {
        autoGenerateKeys: boolean;
        keyStyle?: 'nested' | 'flat' | 'camelCase' | 'kebab-case';
        checkSimilar: boolean;
        overwrite: boolean;
        validateStructure: boolean;
        skipOnError: boolean;
        batchSize: number;
      };
    }) => {
      try {
        const extractor = new TranslationExtractor();
        const style = options.keyStyle || config.keyStyle || 'nested';
        const baseLanguage = config.baseLanguage || 'en';
        
        const results: BulkTranslationResult[] = [];
        const errors: string[] = [];
        let processed = 0;
        let successful = 0;
        let skipped = 0;

        // Process translations in batches
        for (let i = 0; i < translations.length; i += options.batchSize) {
          const batch = translations.slice(i, i + options.batchSize);
          
          for (const translation of batch) {
            try {
              const result = await processSingleTranslation(
                translation,
                extractor,
                index,
                config,
                style,
                baseLanguage,
                options
              );
              
              results.push(result);
              processed++;
              
              if (result.success) {
                successful++;
              } else if (result.skipped) {
                skipped++;
              }
              
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : 'Unknown error';
              errors.push(`Translation ${processed + 1}: ${errorMsg}`);
              
              if (!options.skipOnError) {
                throw new Error(`Batch failed at translation ${processed + 1}: ${errorMsg}`);
              }
              
              results.push({
                success: false,
                key: translation.suggestedKey || 'unknown',
                originalText: translation.text,
                addedLanguages: [],
                fileWriteResults: {},
                skipReason: errorMsg
              });
              
              processed++;
            }
          }
        }

        // Validate structure if requested
        let structureValidation = null;
        if (options.validateStructure && successful > 0) {
          try {
            structureValidation = await index.validateStructure({
              baseLanguage,
              autoFix: false
            });
          } catch (error) {
            structureValidation = {
              error: error instanceof Error ? error.message : 'Unknown validation error'
            };
          }
        }

        // Calculate summary statistics
        const summary = {
          total: translations.length,
          processed,
          successful,
          skipped,
          failed: processed - successful - skipped,
          errors: errors.length
        };

        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              success: errors.length === 0 || options.skipOnError,
              summary,
              results: results.slice(0, 20), // Limit results for readability
              errors: errors.slice(0, 10), // Limit errors for readability
              structureValidation: structureValidation ? {
                valid: structureValidation.valid,
                issues: structureValidation.valid ? null : {
                  missingKeys: Object.keys(structureValidation.missingKeys || {}).length,
                  extraKeys: Object.keys(structureValidation.extraKeys || {}).length,
                  typeMismatches: (structureValidation.typeMismatches || []).length
                }
              } : null,
              performance: {
                batchSize: options.batchSize,
                totalBatches: Math.ceil(translations.length / options.batchSize)
              }
            }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: 'text',
            text: JSON.stringify({
              error: 'Bulk translation operation failed',
              message: error instanceof Error ? error.message : 'Unknown error',
              suggestion: 'Try reducing batch size or enabling skipOnError option'
            }, null, 2)
          }]
        };
      }
    }
  );
}

/**
 * Process a single translation entry
 */
async function processSingleTranslation(
  translation: BulkTranslationEntry,
  extractor: TranslationExtractor,
  index: TranslationIndex,
  config: any,
  style: string,
  baseLanguage: string,
  options: any
): Promise<BulkTranslationResult> {
  // Generate key if not provided
  let keyPath = translation.suggestedKey;
  let generatedKey = false;
  
  if (!keyPath && options.autoGenerateKeys) {
    keyPath = extractor.generateSmartKey(translation.text, translation.context, style);
    generatedKey = true;
  }

  if (!keyPath) {
    return {
      success: false,
      key: 'unknown',
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: 'No key path provided and auto-generation failed'
    };
  }

  // Validate key format
  if (!ExtractionUtils.validateKeyFormat(keyPath, style)) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: `Invalid key format for style: ${style}`
    };
  }

  // Check if key already exists
  if (index.has(keyPath) && !options.overwrite) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipped: true,
      skipReason: 'Translation key already exists (use overwrite=true to replace)'
    };
  }

  // Check for similar existing translations
  if (options.checkSimilar && !options.overwrite) {
    const similarTranslations = await ExtractionUtils.findSimilarTranslations(
      translation.text,
      index,
      0.7
    );

    if (similarTranslations.length > 0) {
      return {
        success: false,
        key: keyPath,
        originalText: translation.text,
        addedLanguages: [],
        fileWriteResults: {},
        skipped: true,
        skipReason: `Similar translations found: ${similarTranslations.map(s => s.keyPath).join(', ')}`
      };
    }
  }

  // Prepare all languages (include base language)
  const allLanguages = { ...translation.languages };
  if (!allLanguages[baseLanguage]) {
    allLanguages[baseLanguage] = translation.text;
  }

  // Add the translations to index
  const operations = Object.entries(allLanguages).map(([lang, value]) => ({
    type: 'set' as const,
    keyPath,
    language: lang,
    value
  }));

  const result = await index.batchUpdate(operations);

  if (!result.success) {
    return {
      success: false,
      key: keyPath,
      originalText: translation.text,
      addedLanguages: [],
      fileWriteResults: {},
      skipReason: `Failed to update index: ${result.errors?.join(', ')}`
    };
  }

  // Write translations to files immediately
  const fileWriteResults: Record<string, { success: boolean; error?: string }> = {};
  for (const [language, value] of Object.entries(allLanguages)) {
    try {
      await writeTranslationToFile(language, keyPath, value, config.translationDir);
      fileWriteResults[language] = { success: true };
    } catch (error) {
      fileWriteResults[language] = { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  return {
    success: true,
    key: keyPath,
    originalText: translation.text,
    addedLanguages: Object.keys(allLanguages),
    fileWriteResults,
    generatedKey
  };
}

/**
 * Write a single translation to its corresponding file
 */
async function writeTranslationToFile(
  language: string,
  keyPath: string,
  value: any,
  translationDir: string
): Promise<void> {
  const filePath = join(translationDir, `${language}.json`);
  
  // Read existing file content
  let existingData: any = {};
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    existingData = JSON.parse(content);
  } catch (error) {
    // File doesn't exist or is invalid, start with empty object
    existingData = {};
  }
  
  // Set the nested value
  setNestedValue(existingData, keyPath, value);
  
  // Write back to file with proper formatting
  const newContent = JSON.stringify(existingData, null, 2);
  await fs.writeFile(filePath, newContent, 'utf-8');
}

/**
 * Set a nested value in an object using dot notation
 */
function setNestedValue(obj: any, keyPath: string, value: any): void {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!key) continue; // Skip empty keys
    
    if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
      current[key] = {};
    }
    current = current[key];
  }
  
  const lastKey = keys[keys.length - 1];
  if (lastKey) {
    current[lastKey] = value;
  }
}
