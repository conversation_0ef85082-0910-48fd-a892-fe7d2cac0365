/**
 * Smart file watcher with Chokidar v4 for translation files
 */

import chokidar, { FSWatcher } from 'chokidar';
import { promises as fs } from 'fs';
import { TranslationIndex } from './translation-index.js';
import { FileWatchError } from '../types/translation.js';
import { debounce } from '../utils/path-parser.js';
import { FileWatcherHandlers } from './file-watcher-handlers.js';
import { EventEmitter } from 'events';

/**
 * Configuration for the file watcher
 */
export interface FileWatcherConfig {
  /** Directory to watch for translation files */
  translationDir: string;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** File patterns to ignore */
  ignored?: string[];
  /** Enable debug logging */
  debug?: boolean;
}

/**
 * Translation file watcher with optimized change detection
 */
export class TranslationFileWatcher extends EventEmitter {
  private watcher?: FSWatcher;
  private readonly debouncedProcessChange: (path: string, eventType: 'add' | 'change') => void;
  private readonly config: Required<FileWatcherConfig>;
  private readonly processedFiles = new Set<string>();

  constructor(
    config: FileWatcherConfig,
    private readonly index: TranslationIndex
  ) {
    super();
    
    this.config = {
      debounceMs: 100,
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**'],
      debug: false,
      ...config
    };

    // Debounce file changes to handle rapid successive writes
    this.debouncedProcessChange = debounce(
      this.processFileChange.bind(this), 
      this.config.debounceMs
    );

    if (this.config.debug) {
      console.log(`📁 FileWatcher configured for: ${this.config.translationDir}`);
    }
  }

  /**
   * Start watching for file changes
   */
  async start(): Promise<void> {
    try {
      // Verify the translation directory exists
      await fs.access(this.config.translationDir);
    } catch (error) {
      throw new FileWatchError(
        `Translation directory does not exist: ${this.config.translationDir}`,
        { path: this.config.translationDir, error }
      );
    }

    this.watcher = chokidar.watch(this.config.translationDir, {
      // Optimized settings for performance
      persistent: true,
      ignoreInitial: false,
      followSymlinks: false,
      
      // Use native fsevents on macOS, fs.watch elsewhere
      usePolling: false,
      
      // Only watch JSON files
      ignored: [
        ...this.config.ignored,
        (path: string) => !path.endsWith('.json')
      ],
      
      // Performance optimizations
      alwaysStat: false,
      depth: 3, // Limit recursion depth
      
      // Reduce OS resource usage
      awaitWriteFinish: {
        stabilityThreshold: this.config.debounceMs,
        pollInterval: Math.max(50, this.config.debounceMs / 2)
      }
    });

    this.watcher
      .on('add', (path: string) => {
        if (this.config.debug) {
          console.log(`📄 File added: ${path}`);
        }
        this.debouncedProcessChange(path, 'add');
      })
      .on('change', (path: string) => {
        if (this.config.debug) {
          console.log(`📝 File changed: ${path}`);
        }
        this.debouncedProcessChange(path, 'change');
      })
      .on('unlink', (path: string) => {
        if (this.config.debug) {
          console.log(`🗑️ File deleted: ${path}`);
        }
        this.handleFileDelete(path);
      })
      .on('error', (error: unknown) => {
        console.error('📁 Watcher error:', error);
        this.emit('error', new FileWatchError('File watcher error', { error }));
      })
      .on('ready', () => {
        if (this.config.debug) {
          console.log(`📁 Initial scan complete. Watching: ${this.config.translationDir}`);
        }
        this.emit('ready');
      });

    if (this.config.debug) {
      console.log(`📁 Started watching translation files in: ${this.config.translationDir}`);
    }
  }

  /**
   * Stop watching for file changes
   */
  async stop(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = undefined;
      this.processedFiles.clear();
      
      if (this.config.debug) {
        console.log('📁 File watcher stopped');
      }
    }
  }

  /**
   * Get list of currently watched files
   */
  getWatchedFiles(): string[] {
    return FileWatcherHandlers.getWatchedFiles(this.watcher);
  }

  /**
   * Manually trigger processing of a file
   */
  async processFile(filePath: string): Promise<void> {
    await FileWatcherHandlers.processFileChange(
      filePath,
      'change',
      this.index,
      this.processedFiles,
      this.config.debounceMs,
      this.config.debug,
      (event, data) => this.emit(event, data)
    );
  }

  /**
   * Process file changes and update the index
   */
  private async processFileChange(filePath: string, eventType: 'add' | 'change'): Promise<void> {
    await FileWatcherHandlers.processFileChange(
      filePath,
      eventType,
      this.index,
      this.processedFiles,
      this.config.debounceMs,
      this.config.debug,
      (event, data) => this.emit(event, data)
    );
  }

  /**
   * Handle file deletion
   */
  private handleFileDelete(filePath: string): void {
    FileWatcherHandlers.handleFileDelete(
      filePath,
      this.index,
      this.config.debug,
      (event, data) => this.emit(event, data)
    );
  }

  /**
   * Get watcher statistics
   */
  getStats(): {
    isWatching: boolean;
    watchedFiles: number;
    processedFiles: number;
  } {
    return {
      isWatching: !!this.watcher,
      watchedFiles: this.getWatchedFiles().length,
      processedFiles: this.processedFiles.size
    };
  }
}
