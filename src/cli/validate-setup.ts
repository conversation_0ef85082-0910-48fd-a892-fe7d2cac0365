#!/usr/bin/env node

/**
 * Setup validation utility for i18n MCP
 */

import { promises as fs } from 'fs';
import { resolve } from 'path';
import { discoverTranslationDirectory } from './config-resolver.js';

interface ValidationResult {
  success: boolean;
  issues: string[];
  warnings: string[];
  info: string[];
}

/**
 * Validate translation directory setup
 */
export async function validateSetup(translationDir?: string): Promise<ValidationResult> {
  const result: ValidationResult = {
    success: true,
    issues: [],
    warnings: [],
    info: []
  };

  try {
    // Discover or validate translation directory
    let targetDir = translationDir;
    if (!targetDir) {
      result.info.push('🔍 Auto-discovering translation directory...');
      targetDir = await discoverTranslationDirectory();
      
      if (!targetDir) {
        result.success = false;
        result.issues.push('❌ No translation directory found');
        result.issues.push('   Try: npx i18n-mcp ./path/to/translations');
        return result;
      }
      
      result.info.push(`✅ Found translation directory: ${targetDir}`);
    }

    const resolvedDir = resolve(targetDir);
    
    // Check if directory exists
    try {
      const stats = await fs.stat(resolvedDir);
      if (!stats.isDirectory()) {
        result.success = false;
        result.issues.push(`❌ Path is not a directory: ${resolvedDir}`);
        return result;
      }
    } catch (error) {
      result.success = false;
      result.issues.push(`❌ Directory does not exist: ${resolvedDir}`);
      return result;
    }

    // Check for JSON files
    const files = await fs.readdir(resolvedDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    if (jsonFiles.length === 0) {
      result.success = false;
      result.issues.push('❌ No JSON translation files found');
      result.issues.push('   Expected files like: en.json, fr.json, de.json');
      return result;
    }

    result.info.push(`✅ Found ${jsonFiles.length} translation files: ${jsonFiles.join(', ')}`);

    // Validate JSON files
    const languages: string[] = [];
    const allKeys = new Set<string>();
    const keysByLanguage: Record<string, Set<string>> = {};

    for (const file of jsonFiles) {
      const filePath = resolve(resolvedDir, file);
      const language = file.replace('.json', '');
      languages.push(language);
      
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        // Extract all keys
        const keys = extractKeys(data);
        keys.forEach(key => allKeys.add(key));
        keysByLanguage[language] = new Set(keys);
        
        result.info.push(`✅ ${language}: ${keys.length} translation keys`);
        
      } catch (error) {
        result.success = false;
        result.issues.push(`❌ Invalid JSON in ${file}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Check for missing translations
    if (languages.length > 1) {
      const baseLanguage = languages.includes('en') ? 'en' : languages[0];
      const baseKeys = keysByLanguage[baseLanguage];
      
      for (const language of languages) {
        if (language === baseLanguage) continue;
        
        const langKeys = keysByLanguage[language];
        const missingKeys = [...baseKeys].filter(key => !langKeys.has(key));
        const extraKeys = [...langKeys].filter(key => !baseKeys.has(key));
        
        if (missingKeys.length > 0) {
          result.warnings.push(`⚠️  ${language} missing ${missingKeys.length} keys from ${baseLanguage}`);
        }
        
        if (extraKeys.length > 0) {
          result.warnings.push(`⚠️  ${language} has ${extraKeys.length} extra keys not in ${baseLanguage}`);
        }
      }
    }

    // Check directory permissions
    try {
      const testFile = resolve(resolvedDir, '.i18n-mcp-test');
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);
      result.info.push('✅ Directory is writable');
    } catch (error) {
      result.warnings.push('⚠️  Directory may not be writable');
    }

    result.info.push(`✅ Setup validation completed`);
    
  } catch (error) {
    result.success = false;
    result.issues.push(`❌ Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Extract all keys from a nested object
 */
function extractKeys(obj: any, prefix = ''): string[] {
  const keys: string[] = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * Print validation results
 */
export function printValidationResults(result: ValidationResult): void {
  console.log('\n🔍 i18n MCP Setup Validation\n');
  
  // Print info messages
  for (const info of result.info) {
    console.log(info);
  }
  
  // Print warnings
  if (result.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    for (const warning of result.warnings) {
      console.log(`   ${warning}`);
    }
  }
  
  // Print issues
  if (result.issues.length > 0) {
    console.log('\n❌ Issues:');
    for (const issue of result.issues) {
      console.log(`   ${issue}`);
    }
  }
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  if (result.success) {
    console.log('✅ Setup validation passed! The i18n MCP should work correctly.');
    console.log('\nNext steps:');
    console.log('1. Start the MCP: npx i18n-mcp');
    console.log('2. Use with your AI assistant');
    console.log('3. Analyze and manage translations');
  } else {
    console.log('❌ Setup validation failed. Please fix the issues above.');
    console.log('\nFor help, see: docs/setup-guide.md');
  }
  console.log('='.repeat(50) + '\n');
}

/**
 * Main function for CLI usage
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const translationDir = args[0];
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
i18n MCP Setup Validator

Usage:
  npx i18n-mcp-validate [directory]
  
Options:
  --help, -h    Show this help message
  
Examples:
  npx i18n-mcp-validate                    # Auto-discover directory
  npx i18n-mcp-validate ./i18n/locales    # Validate specific directory
`);
    return;
  }
  
  try {
    const result = await validateSetup(translationDir);
    printValidationResults(result);
    
    if (!result.success) {
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
