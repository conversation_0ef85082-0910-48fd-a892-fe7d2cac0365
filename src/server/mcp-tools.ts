/**
 * MCP tool definitions for the translation server
 */

import { z } from 'zod';
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
import { ServerConfig } from '../types/translation.js';

// Import advanced tools
import { setupAnalyzeCodeTool } from '../tools/analyze-code-file.js';
import { setupExtractToTranslationTool } from '../tools/extract-to-translation.js';
import { setupAddTranslationSmartTool } from '../tools/add-translation-smart.js';
import { setupGetTranslationSuggestionsTool } from '../tools/get-translation-suggestions.js';
import { setupSyncTranslationsToFilesTool } from '../tools/sync-translations-to-files.js';
import { setupGenerateTypesTool } from '../tools/generate-types.js';

export class MCPTools {
  constructor(
    private readonly index: TranslationIndex,
    private readonly fileWatcher: TranslationFileWatcher,
    private readonly config: Required<ServerConfig>
  ) {}

  /**
   * Register all tools with the MCP server using correct SDK format
   */
  registerTools(server: any): void {
    // Search translation tool
    server.tool(
      'search_translation',
      'Search for translation keys and values',
      {
        query: z.string().describe('Search query for keys or values'),
        scope: z.enum(['keys', 'values', 'both']).default('both').describe('Search scope'),
        languages: z.array(z.string()).optional().describe('Specific languages to search in'),
        maxResults: z.number().min(1).max(100).default(20).describe('Maximum number of results'),
        caseSensitive: z.boolean().default(false).describe('Case sensitive search')
      },
      async ({ query, scope, languages, maxResults, caseSensitive }: any) => {
        try {
          const results = await this.index.search(query, {
            scope,
            languages,
            maxResults,
            caseSensitive
          });

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                query,
                scope,
                resultsCount: results.length,
                results: results.map(result => ({
                  keyPath: result.keyPath,
                  matchType: result.matchType,
                  score: result.score,
                  translations: result.translations
                }))
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error searching translations: ${error instanceof Error ? error.message : 'Unknown error'}`
            }]
          };
        }
      }
    );

    // Get translation context tool
    server.tool(
      'get_translation_context',
      'Get translation context with parent, children, and siblings',
      {
        keyPath: z.string().describe('Translation key path (e.g., "common.buttons.submit")'),
        contextDepth: z.number().min(0).max(5).default(1).describe('Depth of context to retrieve'),
        languages: z.union([z.array(z.string()), z.literal('all')]).default('all').describe('Languages to include')
      },
      async ({ keyPath, contextDepth, languages }: any) => {
        try {
          const context = await this.index.getContext(keyPath, {
            depth: contextDepth,
            languages: languages === 'all' ? undefined : languages
          });

          if (!context) {
            return {
              content: [{
                type: 'text',
                text: `Translation key not found: ${keyPath}`
              }]
            };
          }

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                keyPath: context.keyPath,
                translations: context.translations,
                parent: context.parent,
                children: context.children.slice(0, 10), // Limit children for readability
                siblings: context.siblings.slice(0, 10)  // Limit siblings for readability
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error getting translation context: ${error instanceof Error ? error.message : 'Unknown error'}`
            }]
          };
        }
      }
    );

    // Update translation tool
    server.tool(
      'update_translation',
      'Update translation values for a specific key',
      {
        keyPath: z.string().describe('Translation key path to update'),
        updates: z.record(z.string(), z.any()).describe('Language-value pairs to update'),
        validateStructure: z.boolean().default(true).describe('Validate structure consistency')
      },
      async ({ keyPath, updates, validateStructure }: any) => {
        try {
          // Validate structure if requested
          if (validateStructure) {
            const validation = await this.index.validateStructure();
            if (!validation.valid) {
              return {
                content: [{
                  type: 'text',
                  text: `Structure validation failed before update: ${JSON.stringify(validation.structuralIssues, null, 2)}`
                }]
              };
            }
          }

          // Perform updates
          const operations = Object.entries(updates).map(([language, value]) => ({
            type: 'set' as const,
            keyPath,
            language,
            value
          }));

          const result = await this.index.batchUpdate(operations);

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                success: result.success,
                keyPath,
                updatedLanguages: Object.keys(updates),
                errors: result.errors
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error updating translation: ${error instanceof Error ? error.message : 'Unknown error'}`
            }]
          };
        }
      }
    );

    // Validate structure tool
    server.tool(
      'validate_structure',
      'Validate translation structure consistency across languages',
      {
        baseLanguage: z.string().optional().describe('Base language for validation'),
        fix: z.boolean().default(false).describe('Auto-fix missing translations')
      },
      async ({ baseLanguage, fix }: any) => {
        try {
          const validation = await this.index.validateStructure({
            baseLanguage: baseLanguage || this.config.baseLanguage,
            autoFix: fix
          });

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                valid: validation.valid,
                summary: {
                  missingKeysCount: Object.values(validation.missingKeys).reduce((sum, keys) => sum + keys.length, 0),
                  extraKeysCount: Object.values(validation.extraKeys).reduce((sum, keys) => sum + keys.length, 0),
                  typeMismatchesCount: validation.typeMismatches.length
                },
                details: {
                  missingKeys: validation.missingKeys,
                  extraKeys: validation.extraKeys,
                  typeMismatches: validation.typeMismatches,
                  structuralIssues: validation.structuralIssues
                }
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error validating structure: ${error instanceof Error ? error.message : 'Unknown error'}`
            }]
          };
        }
      }
    );

    // Get server statistics tool
    server.tool(
      'get_stats',
      'Get server and translation index statistics',
      {
        includeDetails: z.boolean().default(false).describe('Include detailed statistics')
      },
      async ({ includeDetails }: any) => {
        try {
          const indexStats = this.index.getStats();
          const watcherStats = this.fileWatcher.getStats();

          const stats = {
            server: {
              name: this.config.name,
              version: this.config.version,
              baseLanguage: this.config.baseLanguage
            },
            index: indexStats,
            watcher: watcherStats
          };

          if (includeDetails) {
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({
                  ...stats,
                  details: {
                    allLanguages: this.index.getLanguages(),
                    sampleKeys: this.index.getKeys().slice(0, 10),
                    watchedFiles: this.fileWatcher.getWatchedFiles().slice(0, 10)
                  }
                }, null, 2)
              }]
            };
          }

          return {
            content: [{
              type: 'text',
              text: JSON.stringify(stats, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error getting statistics: ${error instanceof Error ? error.message : 'Unknown error'}`
            }]
          };
        }
      }
    );

    // Register advanced IDE integration tools
    setupAnalyzeCodeTool(server, this.index);
    setupExtractToTranslationTool(server, this.index, this.config);
    setupAddTranslationSmartTool(server, this.index, this.config);
    setupGetTranslationSuggestionsTool(server, this.index, this.config);
    setupSyncTranslationsToFilesTool(server, this.index, this.fileWatcher, this.config);
    setupGenerateTypesTool(server, this.index, this.config);
  }
}
