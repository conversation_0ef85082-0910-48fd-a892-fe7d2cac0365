/**
 * MCP tool definitions for the translation server
 */
import { TranslationIndex } from '../core/translation-index.js';
import { TranslationFileWatcher } from '../core/file-watcher.js';
import { ServerConfig } from '../types/translation.js';
export declare class MCPTools {
    private readonly index;
    private readonly fileWatcher;
    private readonly config;
    constructor(index: TranslationIndex, fileWatcher: TranslationFileWatcher, config: Required<ServerConfig>);
    /**
     * Register all tools with the MCP server using correct SDK format
     */
    registerTools(server: any): void;
}
//# sourceMappingURL=mcp-tools.d.ts.map