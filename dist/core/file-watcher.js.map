{"version": 3, "file": "file-watcher.js", "sourceRoot": "", "sources": ["../../src/core/file-watcher.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,QAAuB,MAAM,UAAU,CAAC;AAC/C,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AAEpC,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACjE,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAgBtC;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,YAAY;IAQnC;IAPX,OAAO,CAAa;IACX,sBAAsB,CAAsD;IAC5E,MAAM,CAA8B;IACpC,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;IAEpD,YACE,MAAyB,EACR,KAAuB;QAExC,KAAK,EAAE,CAAC;QAFS,UAAK,GAAL,KAAK,CAAkB;QAIxC,IAAI,CAAC,MAAM,GAAG;YACZ,UAAU,EAAE,GAAG;YACf,OAAO,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;YAC1E,KAAK,EAAE,KAAK;YACZ,GAAG,MAAM;SACV,CAAC;QAEF,0DAA0D;QAC1D,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CACpC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,IAAI,CAAC,MAAM,CAAC,UAAU,CACvB,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,cAAc,CACtB,yCAAyC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EACrE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,CAC5C,CAAC;QACJ,CAAC;QAED,kDAAkD;QAClD,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YACxD,qCAAqC;YACrC,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,KAAK;YAErB,mDAAmD;YACnD,UAAU,EAAE,KAAK;YAEjB,wBAAwB;YACxB,OAAO,EAAE;gBACP,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;gBACtB,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;aAC1C;YAED,4BAA4B;YAC5B,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,CAAC,EAAE,wBAAwB;YAElC,2BAA2B;YAC3B,gBAAgB,EAAE;gBAChB,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBAC1C,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;aACvD;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;aACT,EAAE,CAAC,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;YACxC,CAAC;YAED,uDAAuD;YACvD,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC3B,qBAAqB,EAAE,CAAC;gBACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBAChD,OAAO,CAAC,KAAK,CAAC,oCAAoC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC;aACD,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAY,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC,CAAC;aACD,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAY,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC;aACD,EAAE,CAAC,OAAO,EAAE,CAAC,KAAc,EAAE,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,cAAc,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC;aACD,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAChB,qBAAqB,GAAG,IAAI,CAAC;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,uCAAuC,qBAAqB,qBAAqB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7H,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEL,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,6CAA6C,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;YACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAE5B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAChC,MAAM,mBAAmB,CAAC,iBAAiB,CACzC,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,SAA2B;QAC3E,MAAM,mBAAmB,CAAC,iBAAiB,CACzC,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,mBAAmB,CAAC,gBAAgB,CAClC,QAAQ,EACR,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QAKN,OAAO;YACL,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO;YAC1B,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM;YAC3C,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI;SACzC,CAAC;IACJ,CAAC;CACF"}