{"version": 3, "file": "translation-extractor.js", "sourceRoot": "", "sources": ["../../src/core/translation-extractor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AAepC;;GAEG;AACH,MAAM,mBAAmB,GAAkD;IACzE,KAAK,EAAE;QACL,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,OAAO,GAAG,KAAK;QACpD,eAAe,EAAE,iDAAiD;QAClE,cAAc,EAAE,eAAe;KAChC;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,UAAU,GAAG,OAAO;QACzD,eAAe,EAAE,qCAAqC;QACtD,cAAc,EAAE,eAAe;KAChC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,QAAQ,GAAG,KAAK;QACrD,eAAe,EAAE,kCAAkC;QACnD,cAAc,EAAE,eAAe;KAChC;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,OAAO,GAAG,kBAAkB;QACjE,eAAe,EAAE,wDAAwD;QACzE,cAAc,EAAE,eAAe;KAChC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,oBAAoB;IACvB,SAAS,CAAsB;IAEvC,YAAY,SAA8B;QACxC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,QAAgB,EAChB,YAAoB,EACpB,cAAsB;QAEtB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;YAEpF,qBAAqB;YACrB,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAEtD,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACxH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CACd,OAAe,EACf,YAAoB,EACpB,cAAsB;QAEtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,sBAAsB;YACtB,OAAO,OAAO,CAAC,OAAO,CACpB,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,EAChE,MAAM,cAAc,IAAI,CACzB,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAE9D,IAAI,cAAc,GAAG,OAAO,CAAC;QAE7B,+CAA+C;QAC/C,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,OAAO;gBACV,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACrF,MAAM;YACR,KAAK,KAAK;gBACR,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACnF,MAAM;YACR,KAAK,QAAQ;gBACX,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACtF,MAAM;YACR,KAAK,SAAS;gBACZ,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACvF,MAAM;QACV,CAAC;QAED,uBAAuB;QACvB,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QAE3D,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,cAAsB,EAAE,SAA8B;QAC1E,MAAM,eAAe,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;QAEpD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,MAAM,cAAc,IAAI,CAAC;QAClC,CAAC;QAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACpD,OAAO,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAe,EAAE,YAAoB,EAAE,WAAmB;QACpF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEnD,4BAA4B;QAC5B,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,8DAA8D,WAAW,QAAQ,EAAE,GAAG,CAAC,EAClG,KAAK,WAAW,EAAE,CACnB,CAAC;QAEF,8BAA8B;QAC9B,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,UAAU,WAAW,SAAS,EAAE,GAAG,CAAC,EAC/C,KAAK,WAAW,IAAI,CACrB,CAAC;QAEF,qCAAqC;QACrC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,SAAS,WAAW,QAAQ,EAAE,GAAG,CAAC,EAC7C,WAAW,CACZ,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAe,EAAE,YAAoB,EAAE,WAAmB;QAClF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEnD,iCAAiC;QACjC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,mDAAmD,WAAW,QAAQ,EAAE,GAAG,CAAC,EACvF,MAAM,WAAW,GAAG,CACrB,CAAC;QAEF,mCAAmC;QACnC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,UAAU,WAAW,SAAS,EAAE,GAAG,CAAC,EAC/C,KAAK,WAAW,IAAI,CACrB,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe,EAAE,YAAoB,EAAE,WAAmB;QACrF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEnD,wBAAwB;QACxB,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,mDAAmD,WAAW,QAAQ,EAAE,GAAG,CAAC,EACvF,KAAK,WAAW,EAAE,CACnB,CAAC;QAEF,0BAA0B;QAC1B,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,UAAU,WAAW,SAAS,EAAE,GAAG,CAAC,EAC/C,KAAK,WAAW,IAAI,CACrB,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAe,EAAE,YAAoB,EAAE,WAAmB;QACtF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEnD,iCAAiC;QACjC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,mDAAmD,WAAW,QAAQ,EAAE,GAAG,CAAC,EACvF,MAAM,WAAW,GAAG,CACrB,CAAC;QAEF,mCAAmC;QACnC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,IAAI,MAAM,CAAC,UAAU,WAAW,SAAS,EAAE,GAAG,CAAC,EAC/C,KAAK,WAAW,IAAI,CACrB,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAe,EAAE,MAAyB;QAC7D,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,iCAAiC;QACjC,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC3D,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvD,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAI,MAAM,CAAC,cAAc,KAAK,eAAe,EAAE,CAAC;YAC9C,iCAAiC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9C,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;gBACtB,CAAC;qBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChE,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;QACrD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,IAAY;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,gBAAgB,CACd,IAAY,EACZ,OAAgB,EAChB,WAA2D,QAAQ;QAEnE,iBAAiB;QACjB,MAAM,SAAS,GAAG,IAAI;aACnB,WAAW,EAAE;aACb,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;aAC3B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,IAAI,EAAE;aACN,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,oBAAoB;QACpB,IAAI,OAAe,CAAC;QACpB,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,WAAW;gBACd,OAAO,GAAG,SAAS;qBAChB,KAAK,CAAC,GAAG,CAAC;qBACV,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBACvF,IAAI,CAAC,EAAE,CAAC,CAAC;gBACZ,MAAM;YACR,KAAK,YAAY;gBACf,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM;YACR,KAAK,QAAQ,CAAC;YACd;gBACE,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM;QACV,CAAC;QAED,qBAAqB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC1B,OAAO,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAgB;QAC3C,IAAI,CAAC,OAAO;YAAE,OAAO,QAAQ,CAAC;QAE9B,yBAAyB;QACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,oCAAoC,EAAE,EAAE,CAAC,CAAC;QAC7F,OAAO,QAAQ,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,WAAW,CAAC;IAC3E,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,eAAe;IAC1B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,IAAY,EACZ,gBAAqB,EACrB,YAAoB,GAAG;QAEvB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE;gBAClD,KAAK,EAAE,QAAQ;gBACf,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;YAEH,OAAO,OAAO;iBACX,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS,CAAC;iBACvC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC;gBAChB,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,KAAK,EAAE,CAAC,GAAG,EAAE;oBACX,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,OAAO,SAAS,IAAI,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;gBAC7D,CAAC,CAAC,EAAE;gBACJ,KAAK,EAAE,CAAC,CAAC,KAAK;aACf,CAAC,CAAC,CAAC;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,GAAW,EAAE,QAAgB;QACpD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,WAAW;gBACd,OAAO,2CAA2C,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/D,KAAK,YAAY;gBACf,OAAO,uCAAuC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3D,KAAK,MAAM;gBACT,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,KAAK,QAAQ,CAAC;YACd;gBACE,OAAO,uCAAuC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF"}