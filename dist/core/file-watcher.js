/**
 * Smart file watcher with Chokidar v4 for translation files
 */
import chokidar from 'chokidar';
import { promises as fs } from 'fs';
import { FileWatchError } from '../types/translation.js';
import { debounce } from '../utils/path-parser.js';
import { FileWatcherHandlers } from './file-watcher-handlers.js';
import { EventEmitter } from 'events';
/**
 * Translation file watcher with optimized change detection
 */
export class TranslationFileWatcher extends EventEmitter {
    index;
    watcher;
    debouncedProcessChange;
    config;
    processedFiles = new Set();
    constructor(config, index) {
        super();
        this.index = index;
        this.config = {
            debounceMs: 100,
            ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**'],
            debug: false,
            ...config
        };
        // Debounce file changes to handle rapid successive writes
        this.debouncedProcessChange = debounce(this.processFileChange.bind(this), this.config.debounceMs);
        if (this.config.debug) {
            console.log(`📁 FileWatcher configured for: ${this.config.translationDir}`);
        }
    }
    /**
     * Start watching for file changes
     */
    async start() {
        try {
            // Verify the translation directory exists
            await fs.access(this.config.translationDir);
        }
        catch (error) {
            throw new FileWatchError(`Translation directory does not exist: ${this.config.translationDir}`, { path: this.config.translationDir, error });
        }
        this.watcher = chokidar.watch(this.config.translationDir, {
            // Optimized settings for performance
            persistent: true,
            ignoreInitial: false,
            followSymlinks: false,
            // Use native fsevents on macOS, fs.watch elsewhere
            usePolling: false,
            // Only watch JSON files
            ignored: [
                ...this.config.ignored,
                (path) => !path.endsWith('.json')
            ],
            // Performance optimizations
            alwaysStat: false,
            depth: 3, // Limit recursion depth
            // Reduce OS resource usage
            awaitWriteFinish: {
                stabilityThreshold: this.config.debounceMs,
                pollInterval: Math.max(50, this.config.debounceMs / 2)
            }
        });
        this.watcher
            .on('add', (path) => {
            if (this.config.debug) {
                console.log(`📄 File added: ${path}`);
            }
            this.debouncedProcessChange(path, 'add');
        })
            .on('change', (path) => {
            if (this.config.debug) {
                console.log(`📝 File changed: ${path}`);
            }
            this.debouncedProcessChange(path, 'change');
        })
            .on('unlink', (path) => {
            if (this.config.debug) {
                console.log(`🗑️ File deleted: ${path}`);
            }
            this.handleFileDelete(path);
        })
            .on('error', (error) => {
            console.error('📁 Watcher error:', error);
            this.emit('error', new FileWatchError('File watcher error', { error }));
        })
            .on('ready', () => {
            if (this.config.debug) {
                console.log(`📁 Initial scan complete. Watching: ${this.config.translationDir}`);
            }
            this.emit('ready');
        });
        if (this.config.debug) {
            console.log(`📁 Started watching translation files in: ${this.config.translationDir}`);
        }
    }
    /**
     * Stop watching for file changes
     */
    async stop() {
        if (this.watcher) {
            await this.watcher.close();
            this.watcher = undefined;
            this.processedFiles.clear();
            if (this.config.debug) {
                console.log('📁 File watcher stopped');
            }
        }
    }
    /**
     * Get list of currently watched files
     */
    getWatchedFiles() {
        return FileWatcherHandlers.getWatchedFiles(this.watcher);
    }
    /**
     * Manually trigger processing of a file
     */
    async processFile(filePath) {
        await FileWatcherHandlers.processFileChange(filePath, 'change', this.index, this.processedFiles, this.config.debounceMs, this.config.debug, (event, data) => this.emit(event, data));
    }
    /**
     * Process file changes and update the index
     */
    async processFileChange(filePath, eventType) {
        await FileWatcherHandlers.processFileChange(filePath, eventType, this.index, this.processedFiles, this.config.debounceMs, this.config.debug, (event, data) => this.emit(event, data));
    }
    /**
     * Handle file deletion
     */
    handleFileDelete(filePath) {
        FileWatcherHandlers.handleFileDelete(filePath, this.index, this.config.debug, (event, data) => this.emit(event, data));
    }
    /**
     * Get watcher statistics
     */
    getStats() {
        return {
            isWatching: !!this.watcher,
            watchedFiles: this.getWatchedFiles().length,
            processedFiles: this.processedFiles.size
        };
    }
}
//# sourceMappingURL=file-watcher.js.map