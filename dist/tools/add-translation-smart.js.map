{"version": 3, "file": "add-translation-smart.js", "sourceRoot": "", "sources": ["../../src/tools/add-translation-smart.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAGzF;;GAEG;AACH,MAAM,UAAU,4BAA4B,CAAC,MAAW,EAAE,KAAuB,EAAE,MAAW;IAC5F,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,0EAA0E,EAC1E;QACE,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;QACvE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,+BAA+B,CAAC;QACrF,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oBAAoB,CAAC;QAClE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAClF,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,mCAAmC,CAAC;QACxF,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QACvG,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,yCAAyC,CAAC;QAC3F,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,8CAA8C,CAAC;QAC9F,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,6CAA6C,CAAC;KACrG,EACD,KAAK,EAAE,EACL,IAAI,EACJ,SAAS,EACT,YAAY,EACZ,OAAO,EACP,eAAe,EACf,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,iBAAiB,EAWlB,EAAE,EAAE;QACH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;YACtD,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC;YAEjD,+BAA+B;YAC/B,IAAI,OAAO,GAAG,YAAY,CAAC;YAC3B,IAAI,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;gBAChC,OAAO,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,iDAAiD;gCACxD,UAAU,EAAE,kDAAkD;6BAC/D,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,sBAAsB;YACtB,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;gBACvD,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,oBAAoB;gCAC3B,GAAG,EAAE,OAAO;gCACZ,cAAc,EAAE,KAAK;gCACrB,UAAU,EAAE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC;6BAC7D,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,8BAA8B;YAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAClD,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,gCAAgC;gCACvC,GAAG,EAAE,OAAO;gCACZ,aAAa,EAAE,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ;gCAC1G,UAAU,EAAE,oDAAoD;gCAChE,cAAc,EAAE,GAAG,OAAO,MAAM;6BACjC,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,0CAA0C;YAC1C,IAAI,mBAAmB,GAAU,EAAE,CAAC;YACpC,IAAI,YAAY,EAAE,CAAC;gBACjB,mBAAmB,GAAG,MAAM,eAAe,CAAC,uBAAuB,CACjE,IAAI,EACJ,KAAK,EACL,GAAG,CACJ,CAAC;gBAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjD,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oCACnB,OAAO,EAAE,4BAA4B;oCACrC,YAAY,EAAE,OAAO;oCACrB,QAAQ,EAAE,IAAI;oCACd,mBAAmB;oCACnB,OAAO,EAAE,KAAK;oCACd,cAAc,EAAE,qEAAqE;iCACtF,EAAE,IAAI,EAAE,CAAC,CAAC;6BACZ,CAAC;qBACH,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,MAAM,YAAY,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;YACpC,CAAC;YAED,yCAAyC;YACzC,MAAM,kBAAkB,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAChD,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YAEhF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;gBACrD,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,OAAO,EAAE,yCAAyC;gCAClD,GAAG,EAAE,OAAO;gCACZ,gBAAgB;gCAChB,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;gCAC5C,cAAc,EAAE,qEAAqE;6BACtF,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;gBACtE,IAAI,EAAE,KAAc;gBACpB,OAAO;gBACP,QAAQ,EAAE,IAAI;gBACd,KAAK;aACN,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEnD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,4BAA4B;gCACnC,GAAG,EAAE,OAAO;gCACZ,MAAM,EAAE,MAAM,CAAC,MAAM;6BACtB,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,IAAI,mBAAmB,GAAG,IAAI,CAAC;YAC/B,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACH,mBAAmB,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC;wBAClD,YAAY;wBACZ,OAAO,EAAE,KAAK;qBACf,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,yDAAyD;oBACzD,mBAAmB,GAAG;wBACpB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;qBAC3E,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,OAAO,EAAE,IAAI;4BACb,GAAG,EAAE,OAAO;4BACZ,QAAQ,EAAE,IAAI;4BACd,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;4BACzC,QAAQ,EAAE,KAAK;4BACf,WAAW,EAAE,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;4BAC5C,wBAAwB,EAAE,mBAAmB,CAAC,MAAM;4BACpD,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,CAAC;gCACzC,KAAK,EAAE,mBAAmB,CAAC,KAAK;gCAChC,MAAM,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oCACzC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,MAAM;oCACtE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM;oCAClE,cAAc,EAAE,CAAC,mBAAmB,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,MAAM;iCAClE;6BACF,CAAC,CAAC,CAAC,IAAI;yBACT,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC9F,CAAC;aACH,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC"}