/**
 * MCP tool for syncing translation index back to files
 */
import { z } from 'zod';
import { promises as fs } from 'fs';
import { dirname, join } from 'path';
/**
 * Setup the sync translations to files tool
 */
export function setupSyncTranslationsToFilesTool(server, index, fileWatcher, config) {
    server.tool('sync_translations_to_files', 'Sync translation index back to translation files', {
        languages: z.array(z.string()).optional().describe('Specific languages to sync (all if not specified)'),
        dryRun: z.boolean().default(false).describe('Preview changes without writing files'),
        backup: z.boolean().default(true).describe('Create backup files before writing'),
        format: z.enum(['json', 'yaml', 'auto']).default('auto').describe('Output format for files'),
        createMissing: z.boolean().default(true).describe('Create missing language files'),
        sortKeys: z.boolean().default(true).describe('Sort keys alphabetically in output'),
        indent: z.number().min(0).max(8).default(2).describe('Indentation spaces for JSON files')
    }, async ({ languages, dryRun, backup, format, createMissing, sortKeys, indent }) => {
        try {
            const targetLanguages = languages || index.getLanguages();
            const translationDir = config.translationDir;
            const syncResults = [];
            // Get all translation data
            const allKeys = index.getKeys();
            for (const language of targetLanguages) {
                try {
                    const result = await syncLanguageToFile(language, allKeys, index, translationDir, {
                        dryRun,
                        backup,
                        format,
                        createMissing,
                        sortKeys,
                        indent
                    });
                    syncResults.push({
                        language,
                        ...result
                    });
                }
                catch (error) {
                    syncResults.push({
                        language,
                        error: error instanceof Error ? error.message : 'Unknown error',
                        success: false
                    });
                }
            }
            // Calculate summary
            const summary = {
                totalLanguages: targetLanguages.length,
                successful: syncResults.filter(r => r.success).length,
                failed: syncResults.filter(r => r.error).length,
                filesModified: syncResults.filter(r => r.modified).length,
                filesCreated: syncResults.filter(r => r.created).length,
                backupsCreated: syncResults.filter(r => r.backupCreated).length
            };
            return {
                content: [{
                        type: 'text',
                        text: JSON.stringify({
                            synced: !dryRun,
                            dryRun,
                            summary,
                            results: syncResults.map(r => ({
                                language: r.language,
                                success: r.success,
                                modified: r.modified,
                                created: r.created,
                                backupCreated: r.backupCreated,
                                filePath: r.filePath,
                                keyCount: r.keyCount,
                                error: r.error,
                                changes: dryRun ? r.changes : undefined
                            }))
                        }, null, 2)
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `Error syncing translations: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }]
            };
        }
    });
}
/**
 * Sync a single language to its file
 */
async function syncLanguageToFile(language, allKeys, index, translationDir, options) {
    const { dryRun, backup, format, createMissing, sortKeys, indent } = options;
    // Determine file path and format
    const filePath = join(translationDir, `${language}.json`);
    const fileFormat = format === 'auto' ? 'json' : format;
    // Check if file exists
    let fileExists = false;
    let existingContent = '';
    try {
        existingContent = await fs.readFile(filePath, 'utf-8');
        fileExists = true;
    }
    catch (error) {
        if (!createMissing) {
            throw new Error(`File does not exist and createMissing is false: ${filePath}`);
        }
    }
    // Build translation object
    const translations = {};
    let keyCount = 0;
    for (const keyPath of allKeys) {
        const entry = index.get(keyPath, language);
        if (entry && typeof entry === 'object' && 'value' in entry) {
            setNestedValue(translations, keyPath, entry.value);
            keyCount++;
        }
    }
    // Sort keys if requested
    const finalTranslations = sortKeys ? sortObjectKeys(translations) : translations;
    // Generate new content
    let newContent;
    if (fileFormat === 'yaml') {
        // Note: Would need yaml library for proper YAML support
        newContent = JSON.stringify(finalTranslations, null, indent);
    }
    else {
        newContent = JSON.stringify(finalTranslations, null, indent) + '\n';
    }
    // Check if content changed
    const contentChanged = existingContent.trim() !== newContent.trim();
    if (dryRun) {
        return {
            success: true,
            modified: contentChanged,
            created: !fileExists,
            filePath,
            keyCount,
            changes: contentChanged ? {
                before: existingContent.slice(0, 200) + (existingContent.length > 200 ? '...' : ''),
                after: newContent.slice(0, 200) + (newContent.length > 200 ? '...' : ''),
                keyCount
            } : null
        };
    }
    // Create backup if requested and file exists
    let backupCreated = false;
    if (backup && fileExists && contentChanged) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs.copyFile(filePath, backupPath);
        backupCreated = true;
    }
    // Write new content if changed
    if (contentChanged || !fileExists) {
        // Ensure directory exists
        await fs.mkdir(dirname(filePath), { recursive: true });
        await fs.writeFile(filePath, newContent, 'utf-8');
    }
    return {
        success: true,
        modified: contentChanged,
        created: !fileExists,
        backupCreated,
        filePath,
        keyCount
    };
}
/**
 * Set nested value in object using dot notation
 */
function setNestedValue(obj, keyPath, value) {
    const keys = keyPath.split('.');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!key)
            continue; // Skip undefined keys
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }
    const lastKey = keys[keys.length - 1];
    if (lastKey) {
        current[lastKey] = value;
    }
}
/**
 * Recursively sort object keys
 */
function sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
        return obj;
    }
    const sorted = {};
    const keys = Object.keys(obj).sort();
    for (const key of keys) {
        sorted[key] = sortObjectKeys(obj[key]);
    }
    return sorted;
}
/**
 * Utility functions for file synchronization
 */
export class SyncUtils {
    /**
     * Validate translation file format
     */
    static async validateTranslationFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const parsed = JSON.parse(content);
            const errors = [];
            // Check if it's an object
            if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
                errors.push('Translation file must contain a JSON object');
            }
            // Check for circular references
            try {
                JSON.stringify(parsed);
            }
            catch (error) {
                errors.push('Translation file contains circular references');
            }
            return {
                valid: errors.length === 0,
                errors
            };
        }
        catch (error) {
            return {
                valid: false,
                errors: [`Failed to parse translation file: ${error instanceof Error ? error.message : 'Unknown error'}`]
            };
        }
    }
    /**
     * Compare two translation objects and return differences
     */
    static compareTranslations(before, after) {
        const beforeKeys = new Set(this.flattenKeys(before));
        const afterKeys = new Set(this.flattenKeys(after));
        const added = Array.from(afterKeys).filter(key => !beforeKeys.has(key));
        const removed = Array.from(beforeKeys).filter(key => !afterKeys.has(key));
        const modified = [];
        // Check for modified values
        for (const key of beforeKeys) {
            if (afterKeys.has(key)) {
                const beforeValue = this.getNestedValue(before, key);
                const afterValue = this.getNestedValue(after, key);
                if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
                    modified.push(key);
                }
            }
        }
        return { added, removed, modified };
    }
    /**
     * Flatten nested object keys to dot notation
     */
    static flattenKeys(obj, prefix = '') {
        const keys = [];
        for (const [key, value] of Object.entries(obj)) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                keys.push(...this.flattenKeys(value, fullKey));
            }
            else {
                keys.push(fullKey);
            }
        }
        return keys;
    }
    /**
     * Get nested value using dot notation
     */
    static getNestedValue(obj, keyPath) {
        const keys = keyPath.split('.');
        let current = obj;
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            }
            else {
                return undefined;
            }
        }
        return current;
    }
}
//# sourceMappingURL=sync-translations-to-files.js.map