#!/usr/bin/env node
/**
 * Main entry point for the i18n MCP server
 */
import { TranslationMCPServer } from './server/mcp-server.js';
import { promises as fs } from 'fs';
import { resolve } from 'path';
import { parseArgs, loadEnvConfig } from './cli/args-parser.js';
import { setupGracefulShutdown } from './cli/graceful-shutdown.js';
/**
 * Default configuration
 */
const DEFAULT_CONFIG = {
    name: 'i18n-mcp',
    version: '1.0.0',
    baseLanguage: 'en',
    debug: false,
    watchOptions: {
        debounceMs: 100,
        ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**']
    }
};
/**
 * Validate and resolve configuration
 */
async function resolveConfig() {
    const envConfig = loadEnvConfig();
    const argsConfig = parseArgs();
    const config = {
        ...DEFAULT_CONFIG,
        ...envConfig,
        ...argsConfig
    };
    // Determine translation directory
    if (!config.translationDir) {
        // Try common default locations
        const defaultPaths = ['./locales', './i18n', './translations', './lang'];
        for (const defaultPath of defaultPaths) {
            try {
                const resolvedPath = resolve(defaultPath);
                await fs.access(resolvedPath);
                config.translationDir = resolvedPath;
                break;
            }
            catch {
                // Continue to next path
            }
        }
        if (!config.translationDir) {
            console.error('❌ No translation directory found. Please specify with --dir or create ./locales directory.');
            console.error('   Use --help for more information.');
            process.exit(1);
        }
    }
    // Validate translation directory exists
    try {
        const stats = await fs.stat(config.translationDir);
        if (!stats.isDirectory()) {
            console.error(`❌ Translation path is not a directory: ${config.translationDir}`);
            process.exit(1);
        }
    }
    catch (error) {
        console.error(`❌ Translation directory does not exist: ${config.translationDir}`);
        console.error('   Create the directory or specify a different path with --dir');
        process.exit(1);
    }
    return config;
}
/**
 * Main function
 */
async function main() {
    try {
        console.log('🚀 Starting i18n MCP Server...\n');
        // Resolve configuration
        const config = await resolveConfig();
        if (config.debug) {
            console.log('🔧 Configuration:', JSON.stringify(config, null, 2));
        }
        // Create and start server
        const server = new TranslationMCPServer(config);
        // Setup graceful shutdown
        setupGracefulShutdown(server);
        // Start the server
        await server.start();
        // Server is now running and will handle MCP requests via STDIO
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
// Run the main function
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}
export { TranslationMCPServer };
export * from './types/translation.js';
export * from './core/translation-index.js';
export * from './core/file-watcher.js';
//# sourceMappingURL=index.js.map